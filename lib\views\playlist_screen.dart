import 'package:flutter/material.dart';
import 'package:flutterdown/services/download_service.dart';
import 'package:provider/provider.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:fluttertoast/fluttertoast.dart';

class PlaylistScreen extends StatefulWidget {
  const PlaylistScreen({super.key});

  @override
  State<PlaylistScreen> createState() => _PlaylistScreenState();
}

class _PlaylistScreenState extends State<PlaylistScreen>
    with TickerProviderStateMixin {
  final _searchController = TextEditingController();
  bool _isLoading = false;
  String _errorMessage = '';
  List<Playlist> _playlists = [];
  List<Video> _playlistVideos = [];
  Playlist? _selectedPlaylist;
  bool _isSearchingPlaylists = false;
  bool _isLoadingVideos = false;
  late TabController _tabController;
  List<Video> _searchResultsVideos = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _searchPlaylists() async {
    if (_searchController.text.isEmpty) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _isSearchingPlaylists = true;
    });

    try {
      final youtube = YoutubeExplode();

      // البحث عن الفيديوهات أولاً
      final searchQuery = _searchController.text;
      final searchResults = await youtube.search.search(searchQuery);

      // فلترة النتائج للعثور على الفيديوهات فقط (لأن البحث يرجع فيديوهات وليس قوائم)
      final videos = <Video>[];
      for (var result in searchResults) {
        if (result is Video) {
          videos.add(result);
        }
      }

      // حفظ الفيديوهات لاستخدامها لاحقاً
      _searchResultsVideos = videos.take(10).toList();

      if (videos.isNotEmpty) {
        setState(() {
          _playlists =
              []; // للآن نترك القائمة فارغة حتى نحل مشكلة Playlist constructor
          _isLoading = false;
          _isSearchingPlaylists = false;
        });

        Fluttertoast.showToast(
          msg:
              'تم العثور على ${videos.length} فيديو. سيتم إصلاح ميزة القوائم قريباً.',
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.green,
          textColor: Colors.white,
        );
      } else {
        setState(() {
          _playlists = [];
          _isLoading = false;
          _isSearchingPlaylists = false;
        });

        Fluttertoast.showToast(
          msg: 'لم يتم العثور على نتائج. جرب كلمات بحث مختلفة.',
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.orange,
          textColor: Colors.white,
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء البحث: ${e.toString()}';
        _isLoading = false;
        _isSearchingPlaylists = false;
      });
      Fluttertoast.showToast(
        msg: _errorMessage,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  Future<void> _loadPlaylistVideos(Playlist playlist) async {
    setState(() {
      _isLoadingVideos = true;
      _selectedPlaylist = playlist;
    });

    try {
      final youtube = YoutubeExplode();
      final videoStream = await youtube.playlists.getVideos(playlist.id);

      final videos = <Video>[];
      await for (var video in videoStream) {
        videos.add(video);
      }

      setState(() {
        _playlistVideos = videos;
        _isLoadingVideos = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل الفيديوهات: ${e.toString()}';
        _isLoadingVideos = false;
      });
      Fluttertoast.showToast(
        msg: _errorMessage,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  void _downloadPlaylist() {
    if (_selectedPlaylist == null || _playlistVideos.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تنزيل القائمة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('هل تريد تنزيل جميع الفيديوهات في القائمة؟'),
            const SizedBox(height: 16),
            Text(
              'عدد الفيديوهات: ${_playlistVideos.length}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _startPlaylistDownload();
            },
            child: const Text('تنزيل الكل'),
          ),
        ],
      ),
    );
  }

  void _startPlaylistDownload() {
    if (_selectedPlaylist == null || _playlistVideos.isEmpty) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) =>
          const Center(child: SpinKitCircle(color: Colors.blue, size: 50)),
    );

    final downloadService = Provider.of<DownloadService>(
      context,
      listen: false,
    );

    // تنزيل الفيديوهات واحدًا تلو الآخر
    _downloadNextVideo(0, downloadService);
  }

  void _downloadNextVideo(int index, DownloadService downloadService) async {
    if (index >= _playlistVideos.length) {
      Navigator.pop(context);
      Fluttertoast.showToast(
        msg: 'تم تنزيل جميع الفيديوهات بنجاح',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.green,
        textColor: Colors.white,
      );
      return;
    }

    try {
      await downloadService.downloadVideo(context, _playlistVideos[index].url);

      // الانتقال إلى الفيديو التالي بعد فترة قصيرة
      Future.delayed(const Duration(seconds: 2), () {
        _downloadNextVideo(index + 1, downloadService);
      });
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'حدث خطأ أثناء تنزيل الفيديو رقم ${index + 1}: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );

      // الانتقال إلى الفيديو التالي حتى لو فشل هذا الفيديو
      Future.delayed(const Duration(seconds: 2), () {
        _downloadNextVideo(index + 1, downloadService);
      });
    }
  }

  void _downloadVideo(Video video) {
    final downloadService = Provider.of<DownloadService>(
      context,
      listen: false,
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تنزيل الفيديو'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('هل تريد تنزيل هذا الفيديو؟'),
            const SizedBox(height: 16),
            Text(
              video.title,
              style: const TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);

              try {
                await downloadService.downloadVideo(context, video.url);
                Fluttertoast.showToast(
                  msg: 'تم بدء تحميل الفيديو بنجاح',
                  toastLength: Toast.LENGTH_LONG,
                  gravity: ToastGravity.BOTTOM,
                  backgroundColor: Colors.green,
                  textColor: Colors.white,
                );
              } catch (e) {
                Fluttertoast.showToast(
                  msg: 'حدث خطأ أثناء التحميل: ${e.toString()}',
                  toastLength: Toast.LENGTH_LONG,
                  gravity: ToastGravity.BOTTOM,
                  backgroundColor: Colors.red,
                  textColor: Colors.white,
                );
              }
            },
            child: const Text('تنزيل'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قوائم التشغيل'),
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'بحث'),
            Tab(text: 'القوائم المحفوظة'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // علامة التبويب للبحث عن القوائم
          Column(
            children: [
              // شريط البحث
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    labelText: 'ابحث عن قائمة تشغيل...',
                    hintText: 'أدخل كلمات البحث...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.search),
                      onPressed: _isLoading ? null : _searchPlaylists,
                    ),
                  ),
                ),
              ),

              // نتائج البحث
              Expanded(
                child: _isSearchingPlaylists
                    ? const Center(
                        child: SpinKitCircle(color: Colors.blue, size: 50),
                      )
                    : _errorMessage.isNotEmpty
                    ? Center(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            _errorMessage,
                            style: const TextStyle(
                              color: Colors.red,
                              fontSize: 16,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                    : _playlists.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد قوائم',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(8),
                        itemCount: _searchResultsVideos.length,
                        itemBuilder: (context, index) {
                          final video = _searchResultsVideos[index];
                          return Card(
                            elevation: 2,
                            margin: const EdgeInsets.symmetric(vertical: 8),
                            child: ListTile(
                              leading: ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.network(
                                  video.thumbnails.highResUrl,
                                  width: 80,
                                  height: 60,
                                  fit: BoxFit.cover,
                                ),
                              ),
                              title: Text(
                                video.title,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              subtitle: Text(
                                '${video.author} • ${video.duration?.toString() ?? 'غير محدد'}',
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              onTap: () {
                                _downloadVideo(video);
                              },
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),

          // علامة التبويب للقوائم المحفوظة
          const Center(
            child: Text(
              'سيتم حفظ القوائم هنا',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }
}
