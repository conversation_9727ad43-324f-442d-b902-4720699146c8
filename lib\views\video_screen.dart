import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutterdown/services/download_service.dart';
import 'package:provider/provider.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutterdown/views/quality_selection_screen.dart';
import 'package:flutterdown/models/download_type.dart';

class VideoScreen extends StatefulWidget {
  final String videoId;
  const VideoScreen({super.key, required this.videoId});

  @override
  State<VideoScreen> createState() => _VideoScreenState();
}

class _VideoScreenState extends State<VideoScreen> {
  Video? video;
  bool _isLoading = false;
  String _errorMessage = '';
  bool _isDownloaded = false;
  String _downloadPath = '';
  bool _isDownloading = false;

  @override
  void initState() {
    super.initState();
    _fetchVideoDetails();
    _checkIfDownloaded();
  }

  Future<void> _fetchVideoDetails() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final youtube = YoutubeExplode();
      video = await youtube.videos.get(widget.videoId);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء جلب تفاصيل الفيديو: ${e.toString()}';
        _isLoading = false;
      });
      Fluttertoast.showToast(
        msg: _errorMessage,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  Future<void> _checkIfDownloaded() async {
    if (video == null) return;

    try {
      final directory = await getExternalStorageDirectory();
      if (directory == null) {
        return; // لا يمكن الوصول إلى مجلد التخزين
      }

      final String possiblePath =
          '${directory.path}/${video!.title.replaceAll(RegExp(r'[^\w\s-]'), '')}.mp4';
      final file = File(possiblePath);

      if (await file.exists()) {
        setState(() {
          _isDownloaded = true;
          _downloadPath = possiblePath;
        });
      }
    } catch (e) {
      // يمكن تجاهل خطأ التحقق من وجود الملف
    }
  }

  Future<void> _downloadVideo() async {
    if (video == null) return;

    setState(() {
      _isDownloading = true;
    });

    try {
      final downloadService = Provider.of<DownloadService>(
        context,
        listen: false,
      );
      await downloadService.downloadVideo(context, video!.url);

      setState(() {
        _isDownloading = false;
      });
    } catch (e) {
      setState(() {
        _isDownloading = false;
      });

      Fluttertoast.showToast(
        msg: 'حدث خطأ أثناء التنزيل: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الفيديو'),
        centerTitle: true,
        actions: [
          if (video != null)
            IconButton(icon: const Icon(Icons.share), onPressed: _shareVideo),
        ],
      ),
      body: _isLoading
          ? const Center(child: SpinKitCircle(color: Colors.blue, size: 50))
          : _errorMessage.isNotEmpty
          ? Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  _errorMessage,
                  style: const TextStyle(color: Colors.red, fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              ),
            )
          : video == null
          ? const Center(
              child: Text(
                'لا يوجد فيديو',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            )
          : SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // صورة الفيديو
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      video!.thumbnails.highResUrl,
                      width: double.infinity,
                      height: 200,
                      fit: BoxFit.cover,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // معلومات الفيديو
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          video!.title,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'بواسطة: ${video!.author}',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${video!.engagement.viewCount} مشاهدة',
                              style: const TextStyle(fontSize: 16),
                            ),
                            Text(
                              video!.uploadDate != null
                                  ? '${DateFormat('yyyy-MM-dd').format(video!.uploadDate!)}'
                                  : 'تاريخ غير محدد',
                              style: const TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          video!.description,
                          style: const TextStyle(fontSize: 16, height: 1.5),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // أزرار الإجراءات
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.play_arrow),
                                label: const Text('تشغيل'),
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                onPressed: _playVideo,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: _isDownloading
                                  ? const Center(
                                      child: SpinKitCircle(
                                        color: Colors.blue,
                                        size: 30,
                                      ),
                                    )
                                  : ElevatedButton.icon(
                                      icon: Icon(
                                        _isDownloaded
                                            ? Icons.check_circle
                                            : Icons.download,
                                      ),
                                      label: Text(
                                        _isDownloaded
                                            ? 'مُنزَّل'
                                            : 'تنزيل الفيديو',
                                      ),
                                      style: ElevatedButton.styleFrom(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 12,
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        backgroundColor: _isDownloaded
                                            ? Colors.green
                                            : Colors.blue,
                                      ),
                                      onPressed: _isDownloaded
                                          ? null
                                          : _downloadVideo,
                                    ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.music_note),
                                label: const Text('تنزيل الصوت'),
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  backgroundColor: Colors.purple,
                                ),
                                onPressed: _downloadAudio,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.playlist_add),
                                label: const Text('إضافة لقائمة'),
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  backgroundColor: Colors.orange,
                                ),
                                onPressed: _addToPlaylist,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
    );
  }

  void _playVideo() {
    // يمكن هنا استخدام flutter_custom_tabs لفتح الفيديو في يوتيوب
    Fluttertoast.showToast(
      msg: 'سيتم فتح الفيديو في يوتيوب',
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
    );
  }

  void _downloadAudio() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QualitySelectionScreen(
          video: video!,
          downloadType: DownloadType.audio,
        ),
      ),
    );
  }

  void _addToPlaylist() {
    Fluttertoast.showToast(
      msg: 'سيتم إضافة الفيديو إلى قائمة التشغيل',
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
    );
  }

  void _shareVideo() {
    // يمكن هنا استخدام url_launcher لمشاركة رابط الفيديو
    Fluttertoast.showToast(
      msg: 'تم نسخ رابط الفيديو',
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
    );
  }
}
