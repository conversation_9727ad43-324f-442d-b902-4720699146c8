import 'package:flutter/material.dart';
import 'package:flutterdown/services/download_service.dart';
import 'package:provider/provider.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutterdown/views/quality_selection_screen.dart';
import 'package:flutterdown/models/video_quality.dart';
import 'package:flutterdown/models/download_type.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final _searchController = TextEditingController();
  bool _isLoading = false;
  String _errorMessage = '';
  List<Video> _videos = [];
  List<Video> _filteredVideos = [];
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _searchVideos() async {
    if (_searchController.text.isEmpty) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _isSearching = true;
    });

    try {
      final youtube = YoutubeExplode();
      final searchResults = await youtube.search.search(_searchController.text);

      setState(() {
        _videos = searchResults.toList();
        _filteredVideos = _videos;
        _isLoading = false;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء البحث: ${e.toString()}';
        _isLoading = false;
        _isSearching = false;
      });
      Fluttertoast.showToast(
        msg: _errorMessage,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  void _filterVideos(String query) {
    setState(() {
      _filteredVideos = _videos.where((video) {
        return video.title.toLowerCase().contains(query.toLowerCase());
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('بحث'), centerTitle: true),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'ابحث عن فيديو...',
                hintText: 'أدخل كلمات البحث...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.search),
                  onPressed: _isLoading ? null : _searchVideos,
                ),
              ),
            ),
          ),

          // نتائج البحث
          Expanded(
            child: _isSearching
                ? const Center(
                    child: SpinKitCircle(color: Colors.blue, size: 50),
                  )
                : _errorMessage.isNotEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        _errorMessage,
                        style: const TextStyle(color: Colors.red, fontSize: 16),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )
                : _filteredVideos.isEmpty
                ? const Center(
                    child: Text(
                      'لا توجد نتائج',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(8),
                    itemCount: _filteredVideos.length,
                    itemBuilder: (context, index) {
                      final video = _filteredVideos[index];
                      return Card(
                        elevation: 2,
                        margin: const EdgeInsets.symmetric(vertical: 8),
                        child: ListTile(
                          leading: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              video.thumbnails.highResUrl,
                              width: 80,
                              height: 60,
                              fit: BoxFit.cover,
                            ),
                          ),
                          title: Text(
                            video.title,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Text(
                            '${video.author} • ${video.duration?.inMinutes ?? 0}:${((video.duration?.inSeconds ?? 0) % 60).toString().padLeft(2, '0')}',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          onTap: () {
                            // يمكن هنا فتح الفيديو أو إضافة إلى قائمة التنزيل
                            _showVideoOptions(video);
                          },
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  void _showVideoOptions(Video video) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'خيارات الفيديو',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: const Icon(Icons.play_arrow),
                title: const Text('تشغيل الفيديو'),
                onTap: () {
                  Navigator.pop(context);
                  _playVideo(video);
                },
              ),
              ListTile(
                leading: const Icon(Icons.download),
                title: const Text('تنزيل الفيديو'),
                onTap: () {
                  Navigator.pop(context);
                  _downloadVideo(video);
                },
              ),
              ListTile(
                leading: const Icon(Icons.music_note),
                title: const Text('تنزيل الصوت'),
                onTap: () {
                  Navigator.pop(context);
                  _downloadAudio(video);
                },
              ),
              ListTile(
                leading: const Icon(Icons.playlist_play),
                title: const Text('إضافة لقائمة التشغيل'),
                onTap: () {
                  Navigator.pop(context);
                  _addToPlaylist(video);
                },
              ),
              ListTile(
                leading: const Icon(Icons.share),
                title: const Text('مشاركة الفيديو'),
                onTap: () {
                  Navigator.pop(context);
                  _shareVideo(video);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _playVideo(Video video) {
    // يمكن هنا استخدام flutter_custom_tabs لفتح الفيديو في يوتيوب
    Fluttertoast.showToast(
      msg: 'سيتم فتح الفيديو في يوتيوب',
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
    );
  }

  void _downloadVideo(Video video) {
    final downloadService = Provider.of<DownloadService>(
      context,
      listen: false,
    );
    downloadService.downloadVideo(context, video.url);
    Navigator.pop(context);
  }

  void _downloadAudio(Video video) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QualitySelectionScreen(
          video: video,
          downloadType: DownloadType.audio,
        ),
      ),
    );
  }

  void _addToPlaylist(Video video) {
    Fluttertoast.showToast(
      msg: 'سيتم إضافة الفيديو إلى قائمة التشغيل',
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
    );
  }

  void _shareVideo(Video video) {
    // يمكن هنا استخدام url_launcher لمشاركة رابط الفيديو
    Fluttertoast.showToast(
      msg: 'تم نسخ رابط الفيديو',
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
    );
  }
}
