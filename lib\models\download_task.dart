import 'package:flutterdown/models/download_status.dart';
import 'package:flutterdown/models/video.dart';
import 'package:flutterdown/models/video_quality.dart';
import 'package:flutterdown/models/audio_quality.dart';

class DownloadTask {
  String id;
  Video video;
  DownloadTaskStatus status;
  int progress;
  String savePath;
  VideoQuality? videoQuality;
  AudioQuality? audioQuality;
  DateTime? startTime;
  DateTime? completeTime;
  int? fileSize;
  String? errorMessage;
  bool isPaused;
  int retryCount;

  DownloadTask({
    required this.id,
    required this.video,
    required this.status,
    required this.progress,
    required this.savePath,
    this.videoQuality,
    this.audioQuality,
    this.startTime,
    this.completeTime,
    this.fileSize,
    this.errorMessage,
    this.isPaused = false,
    this.retryCount = 0,
  });

  // نسخ مهمة مع تعديل بعض الخصائص
  DownloadTask copyWith({
    String? id,
    Video? video,
    DownloadTaskStatus? status,
    int? progress,
    String? savePath,
    VideoQuality? videoQuality,
    AudioQuality? audioQuality,
    DateTime? startTime,
    DateTime? completeTime,
    int? fileSize,
    String? errorMessage,
    bool? isPaused,
    int? retryCount,
  }) {
    return DownloadTask(
      id: id ?? this.id,
      video: video ?? this.video,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      savePath: savePath ?? this.savePath,
      videoQuality: videoQuality ?? this.videoQuality,
      audioQuality: audioQuality ?? this.audioQuality,
      startTime: startTime ?? this.startTime,
      completeTime: completeTime ?? this.completeTime,
      fileSize: fileSize ?? this.fileSize,
      errorMessage: errorMessage ?? this.errorMessage,
      isPaused: isPaused ?? this.isPaused,
      retryCount: retryCount ?? this.retryCount,
    );
  }

  // حساب السرعة المقدرة للتنزيل
  double? getEstimatedSpeed() {
    if (startTime == null || progress == 0) return null;

    final elapsedSeconds = DateTime.now().difference(startTime!).inSeconds;
    if (elapsedSeconds == 0) return null;

    // تقدير بناءً على التقدم الحالي
    return (progress / 100) * (fileSize ?? 1000000) / elapsedSeconds;
  }

  // التحقق من حالة المهمة
  bool get isActive => status == DownloadTaskStatus.running && !isPaused;
  bool get isCompleted => status == DownloadTaskStatus.complete;
  bool get hasError => status == DownloadTaskStatus.failed;
  bool get canRetry => hasError && retryCount < 3;
}
