import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class NetworkHelper {
  static final Connectivity _connectivity = Connectivity();

  /// التحقق من وجود اتصال بالإنترنت
  static Future<bool> hasInternetConnection() async {
    try {
      // التحقق من نوع الاتصال
      final connectivityResult = await _connectivity.checkConnectivity();
      
      if (connectivityResult.contains(ConnectivityResult.none)) {
        return false;
      }

      // التحقق الفعلي من الاتصال بالإنترنت
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// عرض رسالة خطأ عند عدم وجود اتصال
  static void showNoInternetMessage() {
    Fluttertoast.showToast(
      msg: 'لا يوجد اتصال بالإنترنت. تحقق من الاتصال وحاول مرة أخرى.',
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: Colors.red,
      textColor: Colors.white,
    );
  }

  /// التحقق من الاتصال وعرض رسالة إذا لم يكن متوفراً
  static Future<bool> checkInternetAndShowMessage() async {
    final hasInternet = await hasInternetConnection();
    if (!hasInternet) {
      showNoInternetMessage();
    }
    return hasInternet;
  }

  /// مراقبة تغييرات الاتصال
  static Stream<List<ConnectivityResult>> get connectivityStream =>
      _connectivity.onConnectivityChanged;
}
