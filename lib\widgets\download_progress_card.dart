import 'package:flutter/material.dart';
import 'package:flutterdown/models/download_status.dart';
import 'package:flutterdown/services/download_service.dart';

class DownloadProgressCard extends StatelessWidget {
  final String taskId;
  final DownloadService downloadService;

  const DownloadProgressCard({
    super.key,
    required this.taskId,
    required this.downloadService,
  });

  @override
  Widget build(BuildContext context) {
    final task = downloadService.tasks.firstWhere(
      (t) => t.id == taskId,
      orElse: () => throw Exception('Task not found'),
    );

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الفيديو
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    task.video.thumbnailUrl,
                    height: 60,
                    width: 100,
                    fit: BoxFit.cover,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        task.video.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        task.video.author,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // شريط التقدم
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'التقدم: ${task.progress}%',
                      style: const TextStyle(fontSize: 14),
                    ),
                    Text(
                      _getStatusText(task.status),
                      style: TextStyle(
                        fontSize: 14,
                        color: _getStatusColor(task.status),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: task.progress / 100,
                  backgroundColor: Colors.grey[200],
                  color: _getProgressColor(task.status),
                  minHeight: 8,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // أزرار التحكم
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // زر الاستئناف/إلغاء
                _buildActionButton(
                  icon: task.status == DownloadTaskStatus.running
                      ? Icons.pause
                      : Icons.play_arrow,
                  text: task.status == DownloadTaskStatus.running
                      ? 'إيقاف'
                      : 'استئناف',
                  onPressed: () {
                    if (task.status == DownloadTaskStatus.running) {
                      downloadService.cancelDownload(taskId);
                    } else {
                      downloadService.resumeDownload(taskId);
                    }
                  },
                  color: task.status == DownloadTaskStatus.running
                      ? Colors.orange
                      : Colors.blue,
                ),

                // زر الحذف
                _buildActionButton(
                  icon: Icons.delete,
                  text: 'حذف',
                  onPressed: () {
                    downloadService.deleteFile(taskId);
                  },
                  color: Colors.red,
                ),

                // زر المشاركة
                _buildActionButton(
                  icon: Icons.share,
                  text: 'مشاركة',
                  onPressed: () {
                    downloadService.shareFile(taskId);
                  },
                  color: Colors.green,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String text,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton.icon(
          icon: Icon(icon),
          label: Text(text),
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          onPressed: onPressed,
        ),
      ),
    );
  }

  String _getStatusText(DownloadTaskStatus status) {
    switch (status) {
      case DownloadTaskStatus.enqueued:
        return 'قيد الانتظار';
      case DownloadTaskStatus.running:
        return 'جاري التنزيل';
      case DownloadTaskStatus.complete:
        return 'مكتمل';
      case DownloadTaskStatus.failed:
        return 'فشل';
      case DownloadTaskStatus.canceled:
        return 'ملغى';
      default:
        return 'غير معروف';
    }
  }

  Color _getStatusColor(DownloadTaskStatus status) {
    switch (status) {
      case DownloadTaskStatus.enqueued:
        return Colors.orange;
      case DownloadTaskStatus.running:
        return Colors.blue;
      case DownloadTaskStatus.complete:
        return Colors.green;
      case DownloadTaskStatus.failed:
        return Colors.red;
      case DownloadTaskStatus.canceled:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  Color _getProgressColor(DownloadTaskStatus status) {
    switch (status) {
      case DownloadTaskStatus.running:
        return Colors.blue;
      case DownloadTaskStatus.complete:
        return Colors.green;
      case DownloadTaskStatus.failed:
        return Colors.red;
      case DownloadTaskStatus.canceled:
        return Colors.grey;
      default:
        return Colors.orange;
    }
  }
}
