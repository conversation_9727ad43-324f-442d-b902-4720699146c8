import 'package:flutterdown/models/video.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart' as yt;

class VideoConverter {
  static Video fromYoutubeVideo(yt.Video youtubeVideo) {
    return Video(
      id: youtubeVideo.id.value,
      title: youtubeVideo.title,
      author: youtubeVideo.author,
      duration: youtubeVideo.duration?.toString() ?? '00:00:00',
      thumbnailUrl: youtubeVideo.thumbnails.mediumResUrl,
      url: youtubeVideo.url,
    );
  }

  static List<Video> fromYoutubeVideoList(List<yt.Video> youtubeVideos) {
    return youtubeVideos
        .map((youtubeVideo) => fromYoutubeVideo(youtubeVideo))
        .toList();
  }
}
