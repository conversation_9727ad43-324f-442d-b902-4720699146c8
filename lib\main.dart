import 'package:flutter/material.dart';
import 'package:flutterdown/widgets/bottom_nav_bar.dart';
import 'package:provider/provider.dart';
import 'package:flutterdown/services/download_service.dart';
import 'package:flutterdown/utils/app_localizations.dart';
import 'package:flutterdown/utils/app_settings.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_downloader/flutter_downloader.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة flutter_downloader
  await FlutterDownloader.initialize(
    debug: true, // تفعيل وضع التطوير لرؤية الأخطاء
    ignoreSsl: true, // تجاهل مشاكل SSL
  );

  runApp(
    ChangeNotifierProvider(
      create: (context) => DownloadService(),
      child: const MyApp(),
    ),
  );
}

// دالة لطلب الصلاحيات عند الحاجة (Just-in-time)
Future<bool> requestStoragePermission(BuildContext context) async {
  try {
    // التحقق من الصلاحيات الحالية
    var storageStatus = await Permission.storage.status;

    if (storageStatus.isGranted) {
      return true;
    }

    // عرض dialog لطلب الصلاحية
    bool? userConsent = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('مطلوب صلاحية التخزين'),
          content: const Text(
            'لتنزيل الفيديوهات والصوتيات، يحتاج التطبيق إلى صلاحية الوصول إلى الذاكرة.\n\n'
            'هذه الصلاحية مطلوبة لحفظ الملفات المتنزيلة على جهازك.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('رفض'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('موافقة'),
            ),
          ],
        );
      },
    );

    if (userConsent != true) {
      return false;
    }

    // طلب صلاحيات التخزين
    var status = await Permission.storage.request();

    if (status.isGranted) {
      return true;
    } else if (status.isPermanentlyDenied) {
      // توجيه المستخدم للإعدادات
      await showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('تحتاج لإذن يدوي'),
            content: const Text(
              'تم رفض الصلاحية بشكل دائم. يرجى الذهاب لإعدادات التطبيق وتفعيل صلاحية التخزين يدوياً.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  await openAppSettings();
                },
                child: const Text('الذهاب للإعدادات'),
              ),
            ],
          );
        },
      );
      return false;
    }

    return false;
  } catch (e) {
    debugPrint('خطأ في طلب صلاحية التخزين: $e');
    return false;
  }
}

// دالة لطلب صلاحيات الوسائط عند الحاجة (للأندرويد 13+)
Future<bool> requestMediaPermissions(BuildContext context) async {
  try {
    // التحقق من Android API level
    // في الأندرويد 13+ نحتاج لصلاحيات محددة
    var videoStatus = await Permission.videos.status;
    var audioStatus = await Permission.audio.status;

    // إذا كانت الصلاحيات ممنوحة بالفعل
    if (videoStatus.isGranted && audioStatus.isGranted) {
      return true;
    }

    // عرض dialog لطلب الصلاحيات
    bool? userConsent = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('مطلوب صلاحيات الوسائط'),
          content: const Text(
            'لتطبيق إصدار Android 13 أو أحدث، نحتاج لصلاحيات محددة للوصول للفيديوهات والصوتيات.\n\n'
            'هذه الصلاحيات آمنة ومحدودة فقط لهذا التطبيق.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('رفض'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('موافقة'),
            ),
          ],
        );
      },
    );

    if (userConsent != true) {
      return false;
    }

    // طلب صلاحيات الوسائط المحددة
    videoStatus = await Permission.videos.request();
    audioStatus = await Permission.audio.request();

    return videoStatus.isGranted && audioStatus.isGranted;
  } catch (e) {
    debugPrint('خطأ في طلب صلاحيات الوسائط: $e');
    return false;
  }
}

// دالة شاملة لطلب جميع الصلاحيات المطلوبة
Future<bool> requestAllDownloadPermissions(BuildContext context) async {
  // طلب صلاحية التخزين الأساسية
  bool storageGranted = await requestStoragePermission(context);
  if (!storageGranted) return false;

  // طلب صلاحيات الوسائط المحددة (للأندرويد 13+)
  bool mediaGranted = await requestMediaPermissions(context);
  if (!mediaGranted) return false;

  return true;
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'FlutterDown - تحميل الفيديوهات',
      debugShowCheckedModeBanner: false,
      theme: AppSettings.getTheme(context),
      localizationsDelegates: [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ],
      supportedLocales: const [Locale('ar'), Locale('en')],
      locale: const Locale('ar'),
      home: const PermissionWrapper(),
    );
  }
}

// Widget لطلب الصلاحيات عند بدء التطبيق
class PermissionWrapper extends StatefulWidget {
  const PermissionWrapper({super.key});

  @override
  State<PermissionWrapper> createState() => _PermissionWrapperState();
}

class _PermissionWrapperState extends State<PermissionWrapper> {
  bool _permissionsChecked = false;
  bool _permissionsGranted = false;

  @override
  void initState() {
    super.initState();
    _checkAndRequestPermissions();
  }

  Future<void> _checkAndRequestPermissions() async {
    try {
      // التحقق من الصلاحيات الحالية أولاً
      bool hasPermissions = await _checkCurrentPermissions();

      if (hasPermissions) {
        if (mounted) {
          setState(() {
            _permissionsGranted = true;
            _permissionsChecked = true;
          });
        }
        return;
      }

      // طلب الصلاحيات إذا لم تكن ممنوحة
      bool granted = false;
      if (mounted) {
        granted = await requestAllDownloadPermissions(context);
      }

      if (mounted) {
        setState(() {
          _permissionsGranted = granted;
          _permissionsChecked = true;
        });
      }
    } catch (e) {
      // تسجيل الخطأ بدلاً من print
      debugPrint('خطأ في فحص الصلاحيات: $e');
      if (mounted) {
        setState(() {
          _permissionsGranted = false;
          _permissionsChecked = true;
        });
      }
    }
  }

  Future<bool> _checkCurrentPermissions() async {
    try {
      // فحص صلاحية التخزين العامة أولاً
      var storageStatus = await Permission.storage.status;

      // فحص صلاحيات الوسائط للأندرويد 13+
      var videoStatus = await Permission.videos.status;
      var audioStatus = await Permission.audio.status;

      // طباعة حالة الصلاحيات للتشخيص
      debugPrint('حالة الصلاحيات:');
      debugPrint('Storage: ${storageStatus.name}');
      debugPrint('Videos: ${videoStatus.name}');
      debugPrint('Audio: ${audioStatus.name}');

      // إذا كانت صلاحية التخزين العامة ممنوحة، فهذا كافي
      if (storageStatus.isGranted) {
        debugPrint('صلاحية التخزين العامة ممنوحة');
        return true;
      }

      // إذا لم تكن صلاحية التخزين العامة ممنوحة، تحقق من صلاحيات الوسائط
      // (هذا مطلوب للأندرويد 13+ حيث تم تقسيم صلاحيات التخزين)
      if (videoStatus.isGranted && audioStatus.isGranted) {
        debugPrint('صلاحيات الوسائط ممنوحة');
        return true;
      }

      debugPrint('الصلاحيات غير ممنوحة بالكامل');
      return false;
    } catch (e) {
      debugPrint('خطأ في فحص الصلاحيات: $e');
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_permissionsChecked) {
      // عرض شاشة تحميل أثناء فحص الصلاحيات
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Theme.of(context).primaryColor),
              const SizedBox(height: 20),
              Text(
                'فحص الصلاحيات...',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ],
          ),
        ),
      );
    }

    if (!_permissionsGranted) {
      // عرض شاشة طلب الصلاحيات
      return Scaffold(
        body: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.security,
                  size: 80,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(height: 30),
                Text(
                  'صلاحيات مطلوبة',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                Text(
                  'يحتاج التطبيق إلى صلاحيات التخزين لحفظ الفيديوهات والصوتيات المتنزيلة على جهازك.',
                  style: Theme.of(context).textTheme.bodyLarge,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                ElevatedButton.icon(
                  onPressed: _checkAndRequestPermissions,
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة المحاولة'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 30,
                      vertical: 15,
                    ),
                  ),
                ),
                const SizedBox(height: 15),
                TextButton(
                  onPressed: () async {
                    await openAppSettings();
                  },
                  child: const Text('فتح إعدادات التطبيق'),
                ),
                const SizedBox(height: 10),
                TextButton(
                  onPressed: () {
                    // تخطي فحص الصلاحيات مؤقتاً للاختبار
                    setState(() {
                      _permissionsGranted = true;
                      _permissionsChecked = true;
                    });
                  },
                  child: const Text(
                    'تخطي (للاختبار فقط)',
                    style: TextStyle(color: Colors.orange),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // إذا كانت الصلاحيات ممنوحة، عرض التطبيق الرئيسي
    return const MainScreen();
  }
}
