import 'package:flutter/material.dart';
import 'package:flutterdown/views/home_screen.dart';
import 'package:flutterdown/widgets/bottom_nav_bar.dart';
import 'package:provider/provider.dart';
import 'package:flutterdown/services/download_service.dart';
import 'package:flutterdown/utils/app_localizations.dart';
import 'package:flutterdown/utils/app_settings.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  runApp(
    ChangeNotifierProvider(
      create: (context) => DownloadService(),
      child: const MyApp(),
    ),
  );
}

// دالة لطلب الصلاحيات عند الحاجة (Just-in-time)
Future<bool> requestStoragePermission(BuildContext context) async {
  try {
    // التحقق من الصلاحيات الحالية
    var storageStatus = await Permission.storage.status;

    if (storageStatus.isGranted) {
      return true;
    }

    // عرض dialog لطلب الصلاحية
    bool? userConsent = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('مطلوب صلاحية التخزين'),
          content: const Text(
            'لتنزيل الفيديوهات والصوتيات، يحتاج التطبيق إلى صلاحية الوصول إلى الذاكرة.\n\n'
            'هذه الصلاحية مطلوبة لحفظ الملفات المتنزيلة على جهازك.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('رفض'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('موافقة'),
            ),
          ],
        );
      },
    );

    if (userConsent != true) {
      return false;
    }

    // طلب صلاحيات التخزين
    var status = await Permission.storage.request();

    if (status.isGranted) {
      return true;
    } else if (status.isPermanentlyDenied) {
      // توجيه المستخدم للإعدادات
      await showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('تحتاج لإذن يدوي'),
            content: const Text(
              'تم رفض الصلاحية بشكل دائم. يرجى الذهاب لإعدادات التطبيق وتفعيل صلاحية التخزين يدوياً.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  await openAppSettings();
                },
                child: const Text('الذهاب للإعدادات'),
              ),
            ],
          );
        },
      );
      return false;
    }

    return false;
  } catch (e) {
    print('خطأ في طلب صلاحية التخزين: $e');
    return false;
  }
}

// دالة لطلب صلاحيات الوسائط عند الحاجة (للأندرويد 13+)
Future<bool> requestMediaPermissions(BuildContext context) async {
  try {
    // التحقق من Android API level
    // في الأندرويد 13+ نحتاج لصلاحيات محددة
    var videoStatus = await Permission.videos.status;
    var audioStatus = await Permission.audio.status;

    // إذا كانت الصلاحيات ممنوحة بالفعل
    if (videoStatus.isGranted && audioStatus.isGranted) {
      return true;
    }

    // عرض dialog لطلب الصلاحيات
    bool? userConsent = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('مطلوب صلاحيات الوسائط'),
          content: const Text(
            'لتطبيق إصدار Android 13 أو أحدث، نحتاج لصلاحيات محددة للوصول للفيديوهات والصوتيات.\n\n'
            'هذه الصلاحيات آمنة ومحدودة فقط لهذا التطبيق.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('رفض'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('موافقة'),
            ),
          ],
        );
      },
    );

    if (userConsent != true) {
      return false;
    }

    // طلب صلاحيات الوسائط المحددة
    videoStatus = await Permission.videos.request();
    audioStatus = await Permission.audio.request();

    return videoStatus.isGranted && audioStatus.isGranted;
  } catch (e) {
    print('خطأ في طلب صلاحيات الوسائط: $e');
    return false;
  }
}

// دالة شاملة لطلب جميع الصلاحيات المطلوبة
Future<bool> requestAllDownloadPermissions(BuildContext context) async {
  // طلب صلاحية التخزين الأساسية
  bool storageGranted = await requestStoragePermission(context);
  if (!storageGranted) return false;

  // طلب صلاحيات الوسائط المحددة (للأندرويد 13+)
  bool mediaGranted = await requestMediaPermissions(context);
  if (!mediaGranted) return false;

  return true;
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'FlutterDown - تحميل الفيديوهات',
      debugShowCheckedModeBanner: false,
      theme: AppSettings.getTheme(context),
      localizationsDelegates: [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ],
      supportedLocales: const [Locale('ar'), Locale('en')],
      locale: const Locale('ar'),
      home: const MainScreen(),
    );
  }
}
