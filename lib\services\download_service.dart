import 'dart:io';
import 'dart:async';
import 'dart:collection';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart' as downloader;
import 'package:path_provider/path_provider.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart' as yt;
import 'package:dio/dio.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutterdown/models/video_quality.dart' as my_quality;
import 'package:flutterdown/models/audio_quality.dart';
import 'package:flutterdown/models/download_status.dart' as my_status;
import 'package:flutterdown/models/download_task.dart';
import 'package:flutterdown/models/video.dart';
import 'package:uuid/uuid.dart';
import 'package:path/path.dart' as path;
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';

// استيراد الدوال من main.dart
import 'package:flutterdown/main.dart' as main_app;

// دالة لطلب جميع الصلاحيات المطلوبة
Future<bool> _requestAllDownloadPermissions(BuildContext context) async {
  // طلب صلاحية التخزين الأساسية
  bool storageGranted = await main_app.requestStoragePermission(context);
  if (!storageGranted) return false;

  // طلب صلاحيات الوسائط المحددة (للأندرويد 13+)
  bool mediaGranted = await main_app.requestMediaPermissions(context);
  if (!mediaGranted) return false;

  return true;
}

// استخدام الاسم المستعار لتجنب التعارضات
typedef MyVideoQuality = my_quality.VideoQuality;

class DownloadService extends ChangeNotifier {
  final List<DownloadTask> _tasks = [];
  bool _isDownloading = false;
  String _errorMessage = '';
  String _successMessage = '';
  // قائمة لتخزين التنزيلات المؤقتة
  final Map<String, DownloadTask> _pendingTasks = {};
  // قائمة انتظار التنزيلات
  final Queue<DownloadTask> _waitingQueue = Queue<DownloadTask>();
  // عدد التنزيلات المتزامنة المسموح بها
  static const int _maxConcurrentDownloads = 3;
  // مؤقت لتحديث التقدم
  Timer? _progressTimer;

  // تحويل yt.Video إلى Video مخصص
  Video _convertYtVideoToVideo(yt.Video ytVideo) {
    return Video(
      id: ytVideo.id.value,
      title: ytVideo.title,
      author: ytVideo.author,
      duration: ytVideo.duration?.toString() ?? '00:00:00',
      thumbnailUrl: ytVideo.thumbnails.mediumResUrl,
      url: ytVideo.url,
    );
  }

  List<DownloadTask> get tasks => _tasks;
  List<DownloadTask> get activeTasks =>
      _tasks.where((task) => task.isActive).toList();
  List<DownloadTask> get completedTasks =>
      _tasks.where((task) => task.isCompleted).toList();
  List<DownloadTask> get failedTasks =>
      _tasks.where((task) => task.hasError).toList();
  int get activeDownloadsCount => _tasks
      .where((task) => task.status == my_status.DownloadTaskStatus.running)
      .toList()
      .length;
  bool get canStartNewDownload =>
      activeDownloadsCount < _maxConcurrentDownloads;
  bool get isDownloading =>
      activeDownloadsCount > 0 || _waitingQueue.isNotEmpty;
  String get errorMessage => _errorMessage;
  String get successMessage => _successMessage;
  int get waitingTasksCount => _waitingQueue.length;

  DownloadService() {
    _startProgressUpdater();
    // تأخير تهيئة downloader لتجنب التعارض
    Future.delayed(const Duration(milliseconds: 100), () {
      _initDownloader();
    });
  }

  @override
  void dispose() {
    _stopProgressUpdater();
    super.dispose();
  }

  void _startProgressUpdater() {
    _progressTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateProgress();
    });
  }

  void _stopProgressUpdater() {
    _progressTimer?.cancel();
    _progressTimer = null;
  }

  void _updateProgress() {
    bool hasActiveDownloads = false;

    for (final task in _tasks) {
      if (task.status == my_status.DownloadTaskStatus.running) {
        hasActiveDownloads = true;
        break;
      }
    }

    if (hasActiveDownloads) {
      notifyListeners();
    }
  }

  // بدء تنزيل مهمة محددة
  Future<void> _startDownloadTask(DownloadTask task) async {
    task.status = my_status.DownloadTaskStatus.running;
    task.startTime = DateTime.now();
    notifyListeners();

    try {
      // استخدام Dio للتنزيل المباشر
      final dio = Dio();

      // استخدام youtube_explode لاستخراج الرابط بالجودة المحددة
      final youtubeExplode = yt.YoutubeExplode();

      try {
        // معالجة رابط يوتيوب - استخراج ID من الرابط
        String videoId;
        if (task.video.url.contains('youtube.com') ||
            task.video.url.contains('youtu.be')) {
          try {
            // استخراج ID من الرابط باستخدام تعبير منتظم
            final regExp = RegExp(
              r'(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})',
            );
            final match = regExp.firstMatch(task.video.url);
            if (match != null && match.groupCount >= 1) {
              videoId = match.group(1)!;
            } else {
              throw Exception('لا يمكن استخراج معرف الفيديو من الرابط');
            }
          } catch (e) {
            throw Exception('صيغة رابط يوتيوب غير صحيحة: ${e.toString()}');
          }
        } else {
          // إذا كان الرابط هو ID فقط
          videoId = task.video.id;
        }

        // الحصول على معلومات الفيديو باستخدام ID المستخرج
        final videoDetails = await youtubeExplode.videos.get(videoId);

        // الحصول على دفق الفيديو أو الصوت حسب نوع المهمة
        // استخدام videoId الذي تم استخراجه بدلاً من task.video.id
        yt.StreamManifest manifest = await youtubeExplode.videos.streams
            .getManifest(videoId);

        var selectedStream;

        if (task.audioQuality != null) {
          // للصوت - جلب أفضل تدفق صوتي حسب bitrate
          final audioStreams = manifest.audioOnly.sortByBitrate();
          if (audioStreams.isEmpty) {
            throw Exception('لا توجد تدفقات صوتية متاحة');
          }

          // محاولة العثور على دفق بالجودة المحددة
          if (task.audioQuality != null) {
            final matchingStreams = audioStreams.where((stream) {
              return stream.bitrate.bitsPerSecond == task.audioQuality!.bitrate;
            }).toList();

            selectedStream = matchingStreams.isNotEmpty
                ? matchingStreams.first
                : audioStreams.last;
          } else {
            // استخدم أعلى جودة صوتية متوفرة
            selectedStream = audioStreams.last;
          }

          // تحقق من أن الملف سيتم حفظه بصيغة mp3
          if (!task.savePath.toLowerCase().endsWith('.mp3')) {
            task.savePath = task.savePath.replaceAll(
              RegExp(r'\.(mp4|avi|mov|wmv)$'),
              '.mp3',
            );
          }
        } else {
          // للفيديو - جلب أفضل تدفق فيديو حسب bitrate
          final videoStreams = manifest.muxed.sortByBitrate();
          if (videoStreams.isEmpty) {
            throw Exception('لا توجد تدفقات فيديو متاحة');
          }

          // محاولة العثور على دفق بالجودة المحددة
          if (task.videoQuality != null) {
            final matchingStreams = videoStreams.where((stream) {
              return stream.videoQuality.toString().contains(
                task.videoQuality!.resolution,
              );
            }).toList();

            selectedStream = matchingStreams.isNotEmpty
                ? matchingStreams.first
                : videoStreams.last;
          } else {
            // استخدم أعلى جودة فيديو متوفرة
            selectedStream = videoStreams.last;
          }

          // تحقق من أن الملف سيتم حفظه بصيغة mp4
          if (!task.savePath.toLowerCase().endsWith('.mp4')) {
            task.savePath = task.savePath.replaceAll(
              RegExp(r'\.(avi|mov|wmv)$'),
              '.mp4',
            );
          }
        }

        // الحصول على رابط الدفق
        final streamUrl = selectedStream.url;

        // إضافة معالج لإعادة المحاولة في حالة فشل التنزيل
        int retryCount = 0;
        const maxRetries = 3;
        Response? response;

        while (retryCount < maxRetries) {
          try {
            response = await dio.get(
              streamUrl.toString(),
              options: Options(
                responseType: ResponseType.stream,
                receiveTimeout: const Duration(seconds: 60),
                sendTimeout: const Duration(seconds: 30),
                headers: {
                  'User-Agent':
                      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                },
              ),
            );
            break; // الخروج من حلقة المحاولة في حالة النجاح
          } catch (e) {
            retryCount++;
            if (retryCount >= maxRetries) {
              throw Exception(
                'فشل التنزيل بعد $maxRetries محاولات: ${e.toString()}',
              );
            }
            // الانتظار قبل المحاولة مرة أخرى
            await Future.delayed(const Duration(seconds: 2));
          }
        }

        final file = File(task.savePath);
        final sink = file.openWrite();

        // الحصول على حجم الملف من headers
        int totalBytes = 0;
        final contentLength = response!.headers.value('content-length');
        if (contentLength != null) {
          totalBytes = int.tryParse(contentLength) ?? 0;
        }

        int downloadedBytes = 0;

        response.data.stream.listen(
          (data) {
            downloadedBytes += data.length as int;

            // حساب التقدم بشكل صحيح
            int progress = 0;
            if (totalBytes > 0) {
              progress = ((downloadedBytes / totalBytes) * 100).round();
            } else {
              // إذا لم نحصل على حجم الملف، نعرض تقدم تقريبي
              progress = (downloadedBytes / 1000000 * 10).round().clamp(0, 99);
            }

            // تحديث حالة التنزيل
            task.progress = progress;
            task.fileSize = totalBytes > 0 ? totalBytes : downloadedBytes;
            task.status = my_status.DownloadTaskStatus.running;
            notifyListeners();

            sink.add(data);
          },
          onDone: () async {
            await sink.close();
            task.status = my_status.DownloadTaskStatus.complete;
            task.completeTime = DateTime.now();
            _successMessage = task.audioQuality != null
                ? 'تم استخراج الصوت بنجاح'
                : 'تم تنزيل الفيديو بنجاح';
            notifyListeners();

            Fluttertoast.showToast(
              msg: task.audioQuality != null
                  ? 'تم استخراج الصوت بنجاح'
                  : 'تم تنزيل الفيديو بنجاح',
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
              backgroundColor: Colors.green,
              textColor: Colors.white,
            );

            // محاولة بدء التنزيل التالي من قائمة الانتظار
            _processQueue();
          },
          onError: (error) async {
            await sink.close();
            task.status = my_status.DownloadTaskStatus.failed;
            task.errorMessage = error.toString();
            _errorMessage = task.audioQuality != null
                ? 'حدث خطأ أثناء استخراج الصوت: ${error.toString()}'
                : 'حدث خطأ أثناء التنزيل: ${error.toString()}';
            notifyListeners();

            Fluttertoast.showToast(
              msg: task.audioQuality != null
                  ? 'حدث خطأ أثناء استخراج الصوت'
                  : 'حدث خطأ أثناء التنزيل',
              toastLength: Toast.LENGTH_LONG,
              gravity: ToastGravity.BOTTOM,
              backgroundColor: Colors.red,
              textColor: Colors.white,
            );

            // محاولة بدء التنزيل التالي من قائمة الانتظار
            _processQueue();
          },
        );
      } catch (e) {
        task.status = my_status.DownloadTaskStatus.failed;
        task.errorMessage = e.toString();
        _errorMessage = 'حدث خطأ أثناء معالجة الفيديو: ${e.toString()}';
        notifyListeners();

        // تحديد نوع الخطأ وعرض رسالة مناسبة
        String errorMessage = 'حدث خطأ أثناء معالجة الفيديو';
        if (e.toString().contains('Permission')) {
          errorMessage = 'يرجى منح صلاحيات التخزين للتطبيق';
        } else if (e.toString().contains('Network') ||
            e.toString().contains('SocketException')) {
          errorMessage = 'تحقق من اتصال الإنترنت وحاول مرة أخرى';
        } else if (e.toString().contains('VideoUnavailable') ||
            e.toString().contains('VideoUnplayable')) {
          errorMessage = 'الفيديو غير متاح أو محذوف';
        } else if (e.toString().contains('VideoRequiresPurchase')) {
          errorMessage = 'هذا الفيديو يتطلب شراء';
        } else if (e.toString().contains('VideoPrivate')) {
          errorMessage = 'الفيديو خاص ولا يمكن تنزيله';
        } else if (e.toString().contains('RegionBlocked')) {
          errorMessage = 'الفيديو محجوب في منطقتك';
        } else if (e.toString().contains('timeout')) {
          errorMessage = 'انتهت مهلة الاتصال، حاول مرة أخرى';
        }

        Fluttertoast.showToast(
          msg: errorMessage,
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );

        // محاولة بدء التنزيل التالي من قائمة الانتظار
        _processQueue();
      } finally {
        youtubeExplode.close();
      }
    } catch (e) {
      task.status = my_status.DownloadTaskStatus.failed;
      task.errorMessage = e.toString();
      _errorMessage = 'حدث خطأ: ${e.toString()}';
      notifyListeners();

      Fluttertoast.showToast(
        msg: 'حدث خطأ: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );

      // محاولة بدء التنزيل التالي من قائمة الانتظار
      _processQueue();
    }
  }

  // معالجة قائمة انتظار التنزيلات
  void _processQueue() {
    if (_waitingQueue.isNotEmpty && canStartNewDownload) {
      final nextTask = _waitingQueue.removeFirst();
      _startDownloadTask(nextTask);
    }
  }

  void optimizeMemoryUsage() {
    // حد المهام المكتملة المحتفظ بها (أحدث 50 مهمة مكتملة فقط)
    const maxCompletedTasks = 50;

    final completedTasks = _tasks
        .where((task) => task.status == my_status.DownloadTaskStatus.complete)
        .toList();

    if (completedTasks.length > maxCompletedTasks) {
      // احتفظ بالمهام الأحدث
      completedTasks.sort(
        (a, b) => (b.completeTime ?? b.startTime ?? DateTime.now()).compareTo(
          a.completeTime ?? a.startTime ?? DateTime.now(),
        ),
      );

      final tasksToRemove = completedTasks.sublist(maxCompletedTasks);
      for (final task in tasksToRemove) {
        _tasks.remove(task);
      }
    }

    // إزالة المهام المكتملة القديمة جداً (أكثر من شهر)
    final oneMonthAgo = DateTime.now().subtract(const Duration(days: 30));
    _tasks.removeWhere((task) {
      if (task.status == my_status.DownloadTaskStatus.complete &&
          task.completeTime != null &&
          task.completeTime!.isBefore(oneMonthAgo)) {
        return true;
      }
      return false;
    });

    // تحرير الموارد في المهام الملغاة والفاشلة القديمة
    final oneWeekAgo = DateTime.now().subtract(const Duration(days: 7));
    _tasks.removeWhere((task) {
      if ((task.status == my_status.DownloadTaskStatus.failed ||
              task.status == my_status.DownloadTaskStatus.canceled) &&
          task.startTime != null &&
          task.startTime!.isBefore(oneWeekAgo)) {
        return true;
      }
      return false;
    });

    // تنظيف المهام المؤقتة
    _pendingTasks.clear();

    notifyListeners();

    Fluttertoast.showToast(
      msg:
          'تم تحسين استخدام الذاكرة - تم تنظيف ${_tasks.where((t) => t.isCompleted).length} مهمة مكتملة',
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: Colors.green,
      textColor: Colors.white,
    );
  }

  Future<void> _initDownloader() async {
    try {
      // تسجيل callback فقط، التهيئة تتم في main.dart
      downloader.FlutterDownloader.registerCallback(_downloadCallback);
    } catch (e) {
      print('خطأ في تهيئة downloader: $e');
    }
  }

  static void _downloadCallback(String id, int status, int progress) {
    // يمكن استخدام هذه الدالة لتحديث حالة التنزيل في واجهة المستخدم
  }

  Future<void> requestStoragePermission() async {
    try {
      // محاولة طلب صلاحيات التخزين العامة أولاً
      if (await Permission.storage.isGranted) {
        return;
      }

      var status = await Permission.storage.request();
      if (status.isGranted) {
        return;
      }

      // إذا لم تنجح، جرب صلاحيات الوسائط المحددة لأندرويد 13+
      if (Platform.isAndroid) {
        final videoStatus = await Permission.videos.request();
        final audioStatus = await Permission.audio.request();

        if (videoStatus.isGranted && audioStatus.isGranted) {
          return;
        }
      }

      // إذا فشلت جميع المحاولات
      Fluttertoast.showToast(
        msg: 'يجب منح إذن التخزين للتنزيل',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      throw Exception('تم رفض إذن التخزين');
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'خطأ في طلب الصلاحيات: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      throw Exception('خطأ في طلب الصلاحيات: ${e.toString()}');
    }
  }

  Future<String> getStorageDirectory() async {
    try {
      // محاولة الحصول على مجلد التخزين الخارجي أولاً
      Directory? directory = await getExternalStorageDirectory();
      if (directory != null && await directory.exists()) {
        return directory.path;
      }

      // إذا فشل، استخدم مجلد التطبيق الخاص
      directory = await getApplicationDocumentsDirectory();
      if (directory != null) {
        // إنشاء مجلد خاص بالتنزيلات
        final downloadDir = Directory('${directory.path}/Downloads');
        if (!await downloadDir.exists()) {
          await downloadDir.create(recursive: true);
        }
        return downloadDir.path;
      }

      throw Exception('لا يمكن الوصول إلى مجلد التخزين');
    } catch (e) {
      throw Exception('خطأ في الحصول على مسار التخزين: ${e.toString()}');
    }
  }

  Future<void> downloadVideo(
    BuildContext context,
    String videoUrl, {
    my_quality.VideoQuality? videoQuality,
  }) async {
    try {
      _errorMessage = '';
      notifyListeners();

      // تحقق من BuildContext متاح
      if (context.mounted) {
        // طلب جميع الصلاحيات المطلوبة
        bool permissionsGranted = await _requestAllDownloadPermissions(context);
        if (!permissionsGranted) {
          Fluttertoast.showToast(
            msg: 'يجب منح الصلاحيات للتحميل',
            toastLength: Toast.LENGTH_LONG,
            gravity: ToastGravity.BOTTOM,
            backgroundColor: Colors.red,
            textColor: Colors.white,
          );
          return;
        }
      } else {
        // fallback للطلب التقليدي
        await requestStoragePermission();
      }

      // استخدام تعبير منتظم لاستخراج ID من الرابط
      String videoId;
      if (videoUrl.contains('youtube.com') || videoUrl.contains('youtu.be')) {
        try {
          final regExp = RegExp(
            r'(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})',
          );
          final match = regExp.firstMatch(videoUrl);
          if (match != null && match.groupCount >= 1) {
            videoId = match.group(1)!;
          } else {
            throw Exception('لا يمكن استخراج معرف الفيديو من الرابط');
          }
        } catch (e) {
          throw Exception('صيغة رابط يوتيوب غير صحيحة: ${e.toString()}');
        }
      } else {
        // إذا كان الرابط هو ID فقط
        videoId = videoUrl;
      }

      // الحصول على معلومات الفيديو باستخدام ID
      final youtubeExplode = yt.YoutubeExplode();
      final video = await youtubeExplode.videos.get(videoId);

      // الحصول على مسار التخزين
      final directory = await getStorageDirectory();
      final String fileName = video.title
          .replaceAll(RegExp(r'[^\w\s-]'), '')
          .replaceAll(' ', '_');
      final String savePath = '$directory/$fileName.mp4';

      // التحقق من وجود الملف مسبقًا
      if (await File(savePath).exists()) {
        Fluttertoast.showToast(
          msg: 'الملف موجود بالفعل',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.orange,
          textColor: Colors.white,
        );
        return;
      }

      // إنشاء مهمة تنزيل جديدة
      final task = DownloadTask(
        id: const Uuid().v4(),
        video: _convertYtVideoToVideo(video),
        status: my_status.DownloadTaskStatus.enqueued,
        progress: 0,
        savePath: savePath,
        videoQuality: videoQuality,
        startTime: DateTime.now(),
      );

      _tasks.add(task);

      // التحقق من إمكانية بدء التنزيل مباشرة
      if (canStartNewDownload) {
        await _startDownloadTask(task);
      } else {
        // إضافة إلى قائمة الانتظار
        _waitingQueue.add(task);
        task.status = my_status.DownloadTaskStatus.enqueued;
        Fluttertoast.showToast(
          msg: 'تمت إضافة التنزيل إلى قائمة الانتظار (${_waitingQueue.length})',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.blue,
          textColor: Colors.white,
        );
      }

      notifyListeners();
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'خطأ: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  Future<void> downloadAudio(
    String videoUrl, {
    AudioQuality? audioQuality,
  }) async {
    try {
      _errorMessage = '';
      notifyListeners();

      // طلب إذن التخزين
      await requestStoragePermission();

      // استخدام تعبير منتظم لاستخراج ID من الرابط
      String videoId;
      if (videoUrl.contains('youtube.com') || videoUrl.contains('youtu.be')) {
        try {
          final regExp = RegExp(
            r'(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})',
          );
          final match = regExp.firstMatch(videoUrl);
          if (match != null && match.groupCount >= 1) {
            videoId = match.group(1)!;
          } else {
            throw Exception('لا يمكن استخراج معرف الفيديو من الرابط');
          }
        } catch (e) {
          throw Exception('صيغة رابط يوتيوب غير صحيحة: ${e.toString()}');
        }
      } else {
        // إذا كان الرابط هو ID فقط
        videoId = videoUrl;
      }

      // الحصول على معلومات الفيديو باستخدام ID
      final youtubeExplode = yt.YoutubeExplode();
      final video = await youtubeExplode.videos.get(videoId);

      // الحصول على مسار التخزين
      final directory = await getStorageDirectory();
      final String fileName = video.title
          .replaceAll(RegExp(r'[^\w\s-]'), '')
          .replaceAll(' ', '_');
      final String savePath = '$directory/$fileName.mp3';

      // التحقق من وجود الملف مسبقًا
      if (await File(savePath).exists()) {
        Fluttertoast.showToast(
          msg: 'الملف الصوتي موجود بالفعل',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.orange,
          textColor: Colors.white,
        );
        return;
      }

      // إنشاء مهمة تنزيل جديدة
      final task = DownloadTask(
        id: const Uuid().v4(),
        video: _convertYtVideoToVideo(video),
        status: my_status.DownloadTaskStatus.enqueued,
        progress: 0,
        savePath: savePath,
        audioQuality: audioQuality,
        startTime: DateTime.now(),
      );

      _tasks.add(task);

      // التحقق من إمكانية بدء التنزيل مباشرة
      if (canStartNewDownload) {
        await _startDownloadTask(task);
      } else {
        // إضافة إلى قائمة الانتظار
        _waitingQueue.add(task);
        task.status = my_status.DownloadTaskStatus.enqueued;
        Fluttertoast.showToast(
          msg: 'تمت إضافة التنزيل إلى قائمة الانتظار (${_waitingQueue.length})',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.blue,
          textColor: Colors.white,
        );
      }

      notifyListeners();
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'خطأ: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  void clearMessages() {
    _errorMessage = '';
    _successMessage = '';
    notifyListeners();
  }

  void cancelDownload(String taskId) {
    final taskIndex = _tasks.indexWhere((task) => task.id == taskId);
    if (taskIndex != -1) {
      final task = _tasks[taskIndex];

      // تغيير حالة المهمة إلى ملغاة
      task.status = my_status.DownloadTaskStatus.canceled;
      notifyListeners();

      // إظهار رسالة إلغاء
      Fluttertoast.showToast(
        msg: 'تم إلغاء التنزيل',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.orange,
        textColor: Colors.white,
      );
    }
  }

  Future<void> deleteFile(String taskId) async {
    try {
      final taskIndex = _tasks.indexWhere((task) => task.id == taskId);
      if (taskIndex == -1) return;

      final task = _tasks[taskIndex];
      final file = File(task.savePath);

      if (await file.exists()) {
        await file.delete();

        // إزالة المهمة من القائمة
        _tasks.removeAt(taskIndex);
        notifyListeners();

        // إظهار رسالة الحذف
        Fluttertoast.showToast(
          msg: 'تم حذف الملف',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.green,
          textColor: Colors.white,
        );
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'فشل حذف الملف: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  Future<void> openFile(String taskId) async {
    try {
      final taskIndex = _tasks.indexWhere((task) => task.id == taskId);
      if (taskIndex == -1) return;

      final task = _tasks[taskIndex];
      final file = File(task.savePath);

      if (await file.exists()) {
        // استخدام url_launcher لفتح الملف
        final result = await canLaunchUrl(Uri.file(file.path));

        if (result) {
          await launchUrl(Uri.file(file.path));
        } else {
          Fluttertoast.showToast(
            msg: 'لا يمكن فتح الملف',
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
            backgroundColor: Colors.red,
            textColor: Colors.white,
          );
        }
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'فشل فتح الملف: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  Future<void> shareFile(String taskId) async {
    try {
      final taskIndex = _tasks.indexWhere((task) => task.id == taskId);
      if (taskIndex == -1) return;

      final task = _tasks[taskIndex];
      final file = File(task.savePath);

      if (await file.exists()) {
        // استخدام share_plus لمشاركة الملف
        await Share.shareXFiles([
          XFile(file.path, name: path.basename(file.path)),
        ], subject: 'شارك فيديو: ${task.video.title}');
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'فشل مشاركة الملف: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  Future<void> cleanTempFiles() async {
    try {
      // الحصول على مجلد التطبيق
      final appDir = await getApplicationDocumentsDirectory();
      final tempDir = Directory('${appDir.path}/temp');

      if (await tempDir.exists()) {
        // حذف جميع الملفات والمجلدات في مجلد المؤقت
        await tempDir.delete(recursive: true);

        // إعادة إنشاء مجلد المؤقت
        await tempDir.create(recursive: true);

        Fluttertoast.showToast(
          msg: 'تم تنظيف الملفات المؤقتة',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.green,
          textColor: Colors.white,
        );
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'فشل تنظيف الملفات المؤقتة: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  Future<void> exportDownloadList() async {
    try {
      // الحصول على مجلد التطبيق
      final appDir = await getApplicationDocumentsDirectory();
      final exportDir = Directory('${appDir.path}/exports');

      // إنشاء مجلد التصدير إذا لم يكن موجودًا
      if (!await exportDir.exists()) {
        await exportDir.create(recursive: true);
      }

      // إنشاء اسم ملف فريد
      final fileName =
          'download_list_${DateTime.now().toIso8601String().replaceAll(':', '-').replaceAll('.', '-')}.txt';
      final exportFile = File('${exportDir.path}/$fileName');

      // إنشاء محتوى الملف
      final content = StringBuffer();
      content.writeln('قائمة التنزيلات - ${DateTime.now().toLocal()}');
      content.writeln('==================================\n');

      // إضافة كل مهمة في القائمة
      for (final task in _tasks) {
        content.writeln('العنوان: ${task.video.title}');
        content.writeln('المؤلف: ${task.video.author}');
        content.writeln('الحالة: ${_getStatusText(task.status)}');
        content.writeln('التقدم: ${task.progress}%');
        content.writeln('الحفظ في: ${task.savePath}');
        content.writeln('رابط الفيديو: ${task.video.url}');
        content.writeln('----------------------------------');
      }

      // كتابة المحتوى إلى الملف
      await exportFile.writeAsString(content.toString());

      Fluttertoast.showToast(
        msg: 'تم تصدير قائمة التنزيلات بنجاح',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.green,
        textColor: Colors.white,
      );

      // فتح الملف بعد إنشائه
      if (_tasks.isNotEmpty) {
        await openFile(_tasks.first.id);
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'فشل تصدير قائمة التنزيلات: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  String _getStatusText(my_status.DownloadTaskStatus status) {
    switch (status) {
      case my_status.DownloadTaskStatus.enqueued:
        return 'قيد الانتظار';
      case my_status.DownloadTaskStatus.running:
        return 'جاري التنزيل';
      case my_status.DownloadTaskStatus.complete:
        return 'مكتمل';
      case my_status.DownloadTaskStatus.failed:
        return 'فشل';
      case my_status.DownloadTaskStatus.canceled:
        return 'ملغى';
      default:
        return 'غير معروف';
    }
  }

  Future<void> resumeDownload(String taskId) async {
    try {
      final task = _pendingTasks[taskId];
      if (task == null) {
        throw Exception('لم يتم العثور على المهمة');
      }

      // التحقق مما إذا كان الملف موجودًا بالفعل
      final file = File(task.savePath);
      if (!await file.exists()) {
        throw Exception('الملف غير موجود');
      }

      // الحصول على حجم الملف الحالي
      final currentSize = await file.length();

      // استخدام Dio للتنزيل المباشر مع إضافة رأس للبدء من حجم الملف الحالي
      final dio = Dio();

      // استخدام youtube_explode لاستخراج رابط الفيديو بالجودة المحددة
      final youtubeExplode = yt.YoutubeExplode();

      try {
        // معالجة رابط يوتيوب - استخراج ID من الرابط
        String videoId;
        if (task.video.url.contains('youtube.com') ||
            task.video.url.contains('youtu.be')) {
          try {
            final regExp = RegExp(
              r'(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})',
            );
            final match = regExp.firstMatch(task.video.url);
            if (match != null && match.groupCount >= 1) {
              videoId = match.group(1)!;
            } else {
              throw Exception('لا يمكن استخراج معرف الفيديو من الرابط');
            }
          } catch (e) {
            throw Exception('صيغة رابط يوتيوب غير صحيحة: ${e.toString()}');
          }
        } else {
          // إذا كان الرابط هو ID فقط
          videoId = task.video.id;
        }

        // الحصول على معلومات الفيديو باستخدام ID المستخرج
        final videoDetails = await youtubeExplode.videos.get(videoId);

        // الحصول على دفق الفيديو
        yt.StreamManifest manifest = await youtubeExplode.videos.streams
            .getManifest(videoId);

        final videoStreams = manifest.muxed.sortByBitrate();
        if (videoStreams.isEmpty) {
          throw Exception('لا توجد تدفقات فيديو متاحة');
        }

        var selectedStream = videoStreams.last;

        if (task.videoQuality != null) {
          // محاولة العثور على دفق بالجودة المحددة
          final matchingStreams = videoStreams.where((stream) {
            return stream.videoQuality.toString().contains(
              task.videoQuality!.resolution,
            );
          }).toList();

          if (matchingStreams.isNotEmpty) {
            selectedStream = matchingStreams.first;
          }
        }

        // الحصول على رابط الدفق
        final videoUrl = selectedStream.url;

        // إضافة معالج لإعادة المحاولة في حالة فشل التنزيل
        int retryCount = 0;
        const maxRetries = 3;
        Response? response;

        while (retryCount < maxRetries) {
          try {
            // إضافة رأس للبدء من حجم الملف الحالي
            response = await dio.get(
              videoUrl.toString(),
              options: Options(
                responseType: ResponseType.stream,
                receiveTimeout: const Duration(seconds: 60),
                sendTimeout: const Duration(seconds: 30),
                headers: {'Range': 'bytes=$currentSize-'},
              ),
            );
            break; // الخروج من حلقة المحاولة في حالة النجاح
          } catch (e) {
            retryCount++;
            if (retryCount >= maxRetries) {
              throw Exception(
                'فشل التنزيل بعد $maxRetries محاولات: ${e.toString()}',
              );
            }
            // الانتظار قبل المحاولة مرة أخرى
            await Future.delayed(const Duration(seconds: 2));
          }
        }

        // فتح الملف للإلحاق بدلاً من الكتابة فوقه
        final sink = file.openWrite(mode: FileMode.append);

        int totalBytes = 0;
        int downloadedBytes = currentSize; // البدء من حجم الملف الحالي

        if (response?.data != null) {
          response!.data.stream.listen(
            (data) {
              downloadedBytes += data.length as int;
              // حساب الحجم الكلي إذا لم نكن نعرفه
              if (totalBytes == 0) {
                // محاولة الحصول على الحجم الكلي من رأس Content-Range
                final contentRange = response!.headers.value('Content-Range');
                if (contentRange != null) {
                  final match = RegExp(
                    r'bytes (\d+)-(\d+)/(\d+)',
                  ).firstMatch(contentRange);
                  if (match != null) {
                    totalBytes = int.parse(match.group(3)!);
                  }
                }
              }

              // إذا لم نتمكن من الحصول على الحجم الكلي، استخدم تقدير
              if (totalBytes == 0) {
                totalBytes =
                    downloadedBytes + 1000000; // تقدير بمليون بايت إضافية
              }

              int progress = (downloadedBytes / totalBytes * 100).round();

              // تحديث حالة التنزيل
              task.progress = progress;
              task.status = progress == 100
                  ? my_status.DownloadTaskStatus.complete
                  : my_status.DownloadTaskStatus.running;
              notifyListeners();

              sink.add(data);
            },
            onDone: () async {
              await sink.close();
              _isDownloading = false;
              _successMessage = 'تم استئناف التنزيل بنجاح';
              notifyListeners();

              Fluttertoast.showToast(
                msg: 'تم استئناف التنزيل بنجاح',
                toastLength: Toast.LENGTH_SHORT,
                gravity: ToastGravity.BOTTOM,
                backgroundColor: Colors.green,
                textColor: Colors.white,
              );
            },
            onError: (error) {
              sink.close();
              _isDownloading = false;
              _errorMessage =
                  'حدث خطأ أثناء استئناف التنزيل: ${error.toString()}';
              notifyListeners();

              Fluttertoast.showToast(
                msg: 'حدث خطأ أثناء استئناف التنزيل',
                toastLength: Toast.LENGTH_LONG,
                gravity: ToastGravity.BOTTOM,
                backgroundColor: Colors.red,
                textColor: Colors.white,
              );
            },
          );
        }
      } catch (e) {
        _isDownloading = false;
        _errorMessage = 'حدث خطأ أثناء معالجة الفيديو: ${e.toString()}';
        notifyListeners();

        Fluttertoast.showToast(
          msg: 'حدث خطأ أثناء معالجة الفيديو',
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      } finally {
        youtubeExplode.close();
      }
    } catch (e) {
      _isDownloading = false;
      _errorMessage = 'حدث خطأ: ${e.toString()}';
      notifyListeners();

      Fluttertoast.showToast(
        msg: 'حدث خطأ: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }
}
