import 'package:youtube_explode_dart/youtube_explode_dart.dart' as yt;
import 'package:flutterdown/models/video.dart';
import 'package:flutterdown/utils/video_converter.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutter/material.dart';

class VideoSearch {
  static final yt.YoutubeExplode _youtube = yt.YoutubeExplode();

  /// البحث عن فيديو باستخدام الرابط
  static Future<Video?> searchByVideoUrl(String url) async {
    try {
      final video = await _youtube.videos.get(url);
      return VideoConverter.fromYoutubeVideo(video);
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'خطأ في البحث عن الفيديو: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      return null;
    }
  }

  /// البحث عن قائمة الفيديوهات باستخدام كلمة مفتاحية
  static Future<List<Video>> searchByKeyword(String keyword) async {
    try {
      // البحث عن الفيديوهات
      final searchResults = await _youtube.search.search(keyword);

      // تحويل النتائج إلى نموذج الفيديو الخاص بالتطبيق
      return VideoConverter.fromYoutubeVideoList(searchResults);
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'خطأ في البحث: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      return [];
    }
  }

  /// الحصول على مقترحات البحث
  static Future<List<String>> getSearchSuggestions(String query) async {
    try {
      // API قديم غير مدعوم في الإصدار الحديث
      // يمكن إضافة تطبيق بسيط للمقترحات لاحقاً
      return [];
    } catch (e) {
      return [];
    }
  }

  /// الحصول على قائمة الفيديوهات المقترحة
  static Future<List<Video>> getRecommendedVideos(String videoId) async {
    try {
      // البحث باستخدام معرّف القناة أو كلمات مفتاحية من الفيديو
      // في الإصدارات الحديثة، يمكن استخدام البحث العام للعثور على فيديوهات مشابهة
      final video = await _youtube.videos.get(videoId);
      if (video != null && video.author != '') {
        return await searchByKeyword(video.author);
      }
      return [];
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'خطأ في الحصول على الفيديوهات المقترحة: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      return [];
    }
  }

  /// الحصول على معلومات الفيديو التفصيلية
  static Future<Video?> getVideoDetails(String videoId) async {
    try {
      final video = await _youtube.videos.get(videoId);
      return VideoConverter.fromYoutubeVideo(video);
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'خطأ في الحصول على تفاصيل الفيديو: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      return null;
    }
  }
}
