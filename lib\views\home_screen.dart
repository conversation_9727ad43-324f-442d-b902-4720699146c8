import 'package:flutter/material.dart';
import 'package:flutterdown/services/download_service.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:provider/provider.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'dart:io';
import 'package:flutterdown/utils/app_localizations.dart';
import 'package:flutterdown/utils/network_helper.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final _urlController = TextEditingController();
  bool _isLoading = false;
  String _errorMessage = '';
  List<Video> _videos = [];
  Video? _selectedVideo;
  bool _isSearching = false;
  // خريطة للتخزين المؤقت للفيديوهات المبحوث عنها سابقًا
  final Map<String, Video> _videoCache = {};

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  Future<void> _searchVideo() async {
    if (_urlController.text.isEmpty) return;

    // التحقق من الاتصال بالإنترنت أولاً
    if (!await NetworkHelper.checkInternetAndShowMessage()) {
      return;
    }

    // التحقق من صحة الرابط أولاً
    final url = _urlController.text.trim();
    if (!url.contains('youtube.com') && !url.contains('youtu.be')) {
      setState(() {
        _errorMessage = 'رابط يوتيوب غير صحيح';
      });
      Fluttertoast.showToast(
        msg: 'رابط يوتيوب غير صحيح',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      return;
    }

    // التحقق من وجود الفيديو في الذاكرة المؤقتة
    if (_videoCache.containsKey(url)) {
      setState(() {
        _selectedVideo = _videoCache[url];
        _isLoading = false;
        _isSearching = false;
      });
      Fluttertoast.showToast(
        msg: 'تم العثور على الفيديو في الذاكرة المؤقتة',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.green,
        textColor: Colors.white,
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _isSearching = true;
    });

    try {
      final youtube = YoutubeExplode();

      // إضافة مهلة زمنية للبحث
      final video = await youtube.videos
          .get(url)
          .timeout(
            const Duration(seconds: 15),
            onTimeout: () {
              throw Exception('انتهت مهلة البحث');
            },
          );

      // حفظ الفيديو في الذاكرة المؤقتة
      _videoCache[url] = video;

      setState(() {
        _selectedVideo = video;
        _isLoading = false;
        _isSearching = false;
      });

      youtube.close();
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ: ${e.toString()}';
        _isLoading = false;
        _isSearching = false;
      });
      Fluttertoast.showToast(
        msg: 'حدث خطأ أثناء البحث: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  Future<void> _downloadVideo() async {
    if (_selectedVideo == null) return;

    // التحقق من الاتصال بالإنترنت أولاً
    if (!await NetworkHelper.checkInternetAndShowMessage()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final downloadService = Provider.of<DownloadService>(
        context,
        listen: false,
      );

      // التحقق من الاتصال بالإنترنت
      if (!mounted) return;

      await downloadService.downloadVideo(context, _selectedVideo!.url);

      if (!mounted) return;

      Fluttertoast.showToast(
        msg: 'تم بدء التنزيل بنجاح',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.green,
        textColor: Colors.white,
      );

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      String errorMessage = 'حدث خطأ أثناء التنزيل';
      if (e.toString().contains('Permission')) {
        errorMessage = 'يرجى منح صلاحيات التخزين للتطبيق';
      } else if (e.toString().contains('Network')) {
        errorMessage = 'تحقق من اتصال الإنترنت';
      } else if (e.toString().contains('YouTube')) {
        errorMessage = 'خطأ في الوصول لفيديو يوتيوب';
      }

      Fluttertoast.showToast(
        msg: errorMessage,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              RepaintBoundary(
                child: Text(
                  AppLocalizations.of(context)!.home,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 20),
              RepaintBoundary(
                child: TextField(
                  controller: _urlController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context)!.enterVideoUrl,
                    hintText: 'https://www.youtube.com/watch?v=...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.search),
                      onPressed: _isLoading ? null : _searchVideo,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              if (_isSearching)
                const Center(
                  child: SpinKitCircle(color: Colors.blue, size: 50),
                ),
              if (_errorMessage.isNotEmpty)
                RepaintBoundary(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 10),
                    child: Text(
                      _errorMessage,
                      style: const TextStyle(color: Colors.red),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              if (_selectedVideo != null && !_isLoading)
                Expanded(
                  child: Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              _selectedVideo!.thumbnails.highResUrl,
                              height: 200,
                              width: double.infinity,
                              fit: BoxFit.cover,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _selectedVideo!.title,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            AppLocalizations.of(context)!.search +
                                ': ${_selectedVideo!.author}',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Text(
                                'المدة: ${_selectedVideo!.duration?.inMinutes ?? 0}:${((_selectedVideo!.duration?.inSeconds ?? 0) % 60).toString().padLeft(2, '0')}',
                                style: const TextStyle(fontSize: 16),
                              ),
                              Text(
                                'الزيارات: ${_selectedVideo!.engagement.viewCount}',
                                style: const TextStyle(fontSize: 16),
                              ),
                            ],
                          ),
                          const SizedBox(height: 24),
                          SizedBox(
                            height: 50,
                            child: ElevatedButton.icon(
                              icon: const Icon(Icons.download),
                              label: Text(
                                AppLocalizations.of(context)!.downloadVideo,
                              ),
                              style: ElevatedButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              onPressed: _isLoading ? null : _downloadVideo,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              if (_isLoading && !_isSearching)
                const Expanded(
                  child: Center(
                    child: SpinKitCircle(color: Colors.blue, size: 50),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
